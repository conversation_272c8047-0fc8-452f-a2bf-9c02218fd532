<<<<<<< ORIGINAL
model Post {
  id          String   @id @default(cuid()) @db.VarChar(36)
  userId      String   @db.VarChar(36)
  user        User     @relation("PostUser", fields: [userId], references: [id])
  content     String   @db.Text
  title       String   @db.VarChar(255)
  workflowId  String?  @db.VarChar(255)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
=======
model Post {
  id          String   @id @default(cuid()) @db.VarChar(36)
  userId      String   @db.VarChar(36)
  user        User     @relation("PostUser", fields: [userId], references: [id])
  content     String?  @db.Text
  title       String   @db.VarChar(255)
  workflowId  String?  @db.VarChar(255)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
>>>>>>> UPDATED
