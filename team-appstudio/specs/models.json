{"post": {"owner": false, "canBeCommented": false, "description": "Entity for detailed information about a blog post.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Id of the post."}, "title": {"type": "string", "required": true, "isUser": false, "description": "Title of the post."}, "content": {"type": "string", "required": false, "isUser": false, "description": "Full content of the post."}, "userId": {"type": "string", "required": true, "isUser": false, "description": "Foreign key for the User relation."}, "workflowId": {"type": "string", "required": false, "isUser": false, "description": "ID of the associated workflow."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "Timestamp when the post was last updated."}}}}