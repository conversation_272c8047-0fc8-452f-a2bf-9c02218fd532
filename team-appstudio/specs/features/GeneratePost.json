{"feature": {"name": "Generate Post", "description": "Allows users to generate blog posts using AI based on a topic prompt.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load"}, "where": "/generate-post"}, {"who": "User", "if": null, "action": {"type": "fill", "element": {"type": "input", "eventId": "topic_input"}}, "where": "/generate-post"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button", "eventId": "generate_button"}}, "where": "/generate-post"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "generatePost_request_1", "notify": null}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_button"}}, "where": "/generate-post"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "generatePost_request_1"}}, "where": "/generate-post"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}], "screens": {"/generate-post": "The main screen for users to input a topic and generate a blog post using AI.", "/workflow/:id": "The screen that checks the generation process of the post.", "/posts/:id": "The screen that displays the AI-generated blog post."}, "conditions": ["User provides a topic for the post.", "AI service is available for post generation."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": false, "description": "Entity for a blog post generated by AI.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the post."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "ID of the user who generated the post."}, "title": {"type": "string", "required": true, "description": "Title of the generated post."}, "content": {"type": "string", "required": true, "description": "Full content of the generated post."}, "topic": {"type": "string", "required": true, "description": "The topic provided for AI generation."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for managing the workflow process of resource generation.", "fields": {"id": {"type": "string", "required": true, "description": "Id of the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'in progress', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": false, "description": "Id of the resource being processed by the workflow."}, "resourceType": {"type": "string", "required": false, "description": "Type of the resource being processed (e.g., 'post', 'image', 'audio')."}}}}, "requests": [{"requestId": "generatePost_request_1", "isArray": false, "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating post content with AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing the generated post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "AI assistant", "objective": "Generate a blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": null}, {"requestId": "readWorkflow_request_2", "isArray": false, "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_3", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}