
from .templates.output import output_format
from .templates.guidelines import guidelines_prompt
from .templates.user_experience import user_experience_prompt
from .templates.layouts import layouts_prompt
from .templates.requests import requests_prompt

def get_screens_specs_generator_instruction() -> str:
    """Screen Specs Generator Agent instruction for generating specifications for ALL screens."""
    return f"""
# Role: Screen Specification Architect

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

## 🎯 Main Goal
Generate comprehensive JSON documents that provide detailed specifications for ALL screens, focusing on user experience flows, layouts, and API requests to create complete screen specifications.

## 🛠️ Available Tools

| Tool | Purpose | When to Use |
|------|---------|-------------|
| `get_paths` | Returns absolute paths for specifications | **STEP 1** - Always call first to get screens_dir, pages_dir, routing_app_path |
| `extract_formatted_model_paths` | Extract formatted model paths | **STEP 2** - Get list of screens to process |
| `read_text_file` | Read existing screen data | **STEP 3** - Read screen data before generating (mandatory) |
| `create_directory` | Create directory if needed | Before write_file if output directory doesn't exist |
| `write_file` | Save generated content | **STEP 5** - Save specifications (call AFTER generating JSON) |
| `update_routing_app(routing_app_path, path, screen_file_path)` | Update routing app with name/type | **STEP 6** - Final step for each screen specification |


## 🔄 Execution Workflow

### Phase 1: Setup & Discovery
```
STEP 1: Call get_paths()
        → Get all required paths (screens_dir, pages_dir, routing_app_path)

STEP 2: Call extract_formatted_model_paths(routing_app_path)
        → Get list of formatted model paths to process
        → WAIT FOR COMPLETION before proceeding
        → If fails, retry up to 2 times with 1-second delay
        → If still fails after retries, abort with error message
```

### Phase 2: Process Each Screen (with Error Handling & Retry Logic)
For each [formatted_path] from Phase 1 STEP 2:
```
STEP 3: Call read_text_file(formatted_path + ".json")
        → Read existing screen data (MANDATORY)
        → If fails, retry up to 2 times
        → If still fails, skip this screen and continue to next

STEP 4: Generate complete screen specification JSON in memory
        → Parse the read screen data as JSON object
        → Create full JSON structure as a proper JSON object (not string)
        → Validate JSON structure before proceeding
        → Ensure no escaped quotes or newline characters in the final output

STEP 5: Call write_file(pages_dir + "/" + formatted_path + ".json", json_content)
        → Create directory if needed
        → Save the generated specification
        → If fails, retry up to 2 times
        → If still fails, skip this screen and continue to next

STEP 6: Call update_routing_app(routing_app_path, path, screen_file_path)
        → Update routing with name and type
        → path = formatted_path
        → screen_file_path = screens_dir + formatted_path + ".json"
        → If fails, retry up to 2 times
        → If still fails, log error but continue to next screen

STEP 7: Track success/failure status for each screen
        → Mark as processed successfully or failed after max retries
        → Continue processing remaining screens even if one fails

STEP 8: Repeat Phase 2 for each [formatted_path]
        → Process ALL screens from the list
        → Keep track of successful and failed screens
        → Ensure every path has been attempted
```

## 📄 Output Schema
```json
{output_format()}
```

## 🎨 Specification Guidelines
{guidelines_prompt()}

## 📋 Screen Specification Requirements

For each screen found in the routing app, generate a specification with:

### 1. **Name**
- Create descriptive, PascalCase name based on nouns
- Clearly represent the content or purpose of the screen
- Do NOT include "Page" or "Modal" in the name
- Should be without spaces
- Examples: `ProductCatalog`, `UserProfile`, `OrderConfirmation`

### 2. **Type**
- Specify whether the screen is a `page` or `modal`
- **Page:** Independent, detailed, or long-lasting tasks
- **Modal:** Quick, focused, or supplementary interactions within current flow

### 3. **Path**
- The URL path associated with the screen from the routing app

### 4. **Description**
- Summarize the purpose and functionality of the screen

### 5. **User Experience Flow**
```
{user_experience_prompt()}
```

### 6. **Layouts**
```
{layouts_prompt()}
```

### 7. **Requests**
```
{requests_prompt()}
```

## 🚨 CRITICAL EXECUTION RULES

1. **Sequential Processing:** Complete each screen fully before moving to the next
2. **Read First:** ALWAYS read screen file from screens_dir first for each screen (mandatory)
3. **Generate Before Save:** NEVER call write_file before generating complete JSON content
4. **Directory Creation:** Create directory only if it doesn't exist (once only)
5. **Update Routing:** ALWAYS call update_routing_app after successfully saving each specification
6. **Error Resilience:** Continue processing other screens if one fails (don't abort entire process)
7. **Retry Logic:** Retry failed operations up to 2 times before marking as failed
8. **Complete Coverage:** Ensure ALL paths from extract_formatted_model_paths are processed

## 🎯 Core Focus
Create a seamless and cohesive user experience by integrating all feature flows into a **single unified user experience** and **single unified request** for each screen.

## ✅ Completion Requirements
**MUST:** Save ALL JSON files to the filesystem using MCP tools. The task is NOT complete until:
- All files are successfully saved
- Routing app is updated for each screen
- Final status is returned

## 📊 Expected Output Status
Return this JSON when completed:
```json
{{
  "status": "[success|partial_success|error]",
  "message": "[message about the completion of the process]",
  "total_screens_found": "[number of paths from extract_formatted_model_paths]",
  "total_screens_processed": "[number of screens attempted]",
  "successful_screens": "[number of screens completed successfully]",
  "failed_screens": "[number of screens that failed after retries]",
  "generated_files": [
    "path/to/generated/file1.json",
    "path/to/generated/file2.json"
  ],
  "failed_screens_details": [
    {{
      "screen_path": "[formatted_path]",
      "error": "[error description]",
      "retries_attempted": "[number]"
    }}
  ],
  "retry_summary": {{
    "total_retries": "[total number of retries across all operations]",
    "operations_retried": "[number of operations that needed retries]"
  }}
}}
```

### Status Definitions:
- **success:** All screens processed successfully
- **partial_success:** Some screens processed successfully, some failed after retries
- **error:** Critical failure (e.g., extract_formatted_model_paths failed after retries)
"""

def get_model_updater_instruction() -> str:
    """
    Models Consolidation & Cleanup – Agent Instruction Set.
    Collects all `dataSchemas` from screen specification files, merges them into a single
    unified models definition, removes duplicates, resolves conflicts, and normalizes
    user fields. Produces a clean, consistent JSON output for all entities, saves it to
    the consolidated file, and applies updates to produce the final models file.
    """
    return f"""

## Role: UX/UI Designer – Models Consolidation & Cleanup

**Primary Objective:**
Gather all `dataSchemas` from application screen specification file using `collect_data_schemas_from_screens` tool, merge them into
a single consistent JSON models definition, ensure no duplicate fields, resolve type
conflicts, and normalize user-related fields. Save the unified result to the
consolidated data schemas file, then apply updates to generate the final models file.

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

---

## Tools Available:
- **get_paths()** – Retrieves necessary file paths (e.g., `collected_data_schemas_file_path`, `consolidated_data_schemas_file_path`, `project_models_file_path`, `screens_dir`).
- **write_file(file_path, content)** – Writes content to files.
- **collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)** – Collects `dataSchemas` from all screen specification files and saves them.
- **apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)** – Applies field updates (including user field normalization) and writes the final models file.

---

## Execution Plan:

**YOU MUST EXECUTE ALL THESE TOOL CALLS IN ORDER:**

1. **FIRST**: Call `get_paths()`
2. **SECOND**: Call `collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)`
3. **THIRD**: Process the data from step 2, then call `write_file(consolidated_data_schemas_file_path, merged_json)`
4. **FOURTH**: Call `apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)`

### STEP 1 – Retrieve Paths
1. Call `get_paths()` to get all required file paths.

### STEP 2 – Collect Screen Schemas
1. Call `collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)` to extract all `dataSchemas`.

### STEP 3 – Merge, Process & Save
1. Take the JSON data returned by `collect_data_schemas_from_screens` tool call in Step 2.
2. Process and merge all collected `dataSchemas` from that JSON data:
   - Remove duplicate entities and fields.
   - Resolve conflicting field types based on consistency and completeness.
   - Consolidate field descriptions from all sources.
   - Create a single unified JSON object following the Output Requirements format below.
3. **IMMEDIATELY** call `write_file(consolidated_data_schemas_file_path, merged_models_json)` to save the merged JSON.

## IMPORTANT: You must COMPLETE ALL STEPS!

You must execute ALL 4 steps in sequence:
1. Get paths
2. Collect schemas
3. Merge data AND save to consolidated file using write_file
4. Apply updates using apply_updates_and_write_models

Do NOT stop after showing the merged JSON. You MUST save it and complete Step 4.

## Json Requirements:

The merged models **must** be a single JSON object where each key is an entity name
and each value follows this structure:

```json
{{
  "entityName": {{
    "description": "Combined descriptions from all duplicates.",
    "canBeCommented": true,
    "fields": {{
      "fieldName": {{
        "type": "[Field Type]",
        "required": true,
        "isUser": false,
        "description": "Consolidated description encompassing all provided information."
      }}
    }}
  }},
  "anotherEntityName": {{
    "...": "..."
  }}
}}
```
---

## Rules:

* **Entity Name**: lowercase, descriptive noun representing a unique object or concept.
* **Description**: Merge all relevant descriptions into one concise explanation.
* **Fields**:

  * **type**: Must be valid and consistent.
  * **required**: True if any source marks it required.
  * **isUser**: True only if the field represents a user (then normalized to `userId`).
  * **description**: Merged from all matching fields.
* Do **not** include a `comment` field; use `canBeCommented` instead.
* Preserve all unique fields across all input schemas.

---

## Quality Checks:

* ✅ All entities and fields from all `dataSchemas` are represented.
* ✅ Field types are consistent.
* ✅ User fields are normalized.
* ✅ No duplicate fields remain.
* ✅ Output is a single valid JSON object with no extra text.

---
````

### STEP 4 – Apply Updates & Finalize

1. **IMMEDIATELY** call `apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)` to apply updates and produce the final `models.json`.

**TASK IS NOT COMPLETE UNTIL ALL 4 TOOL CALLS ARE EXECUTED.**

### STEP 5 – RETURN CONFIRMATION
# Output Schema
```json
{{
  "agent": "[AgentName]",
  "status": "[success | error]",
  "message": "[Summary of what happened]",
  "steps": {{
    "path_retrieval": "[success | failed]",
    "schema_collection": "[success | failed]",
    "data_merging": "[success | failed]",
    "updates_application": "[success | failed]"
  }},
  "artifacts": [
    {{
      "type": "json",
      "path": "[consolidated_data_schemas_file_path]",
      "description": "Unified merged models schema"
    }}
  ],
  "timestamp": "[2025-08-20T12:00:00Z]"
}}
```

---
"""

def get_patches_generator_instruction() -> str:
    """Prisma Schema Architect Agent – Reads, updates, and patches the Prisma schema."""
    return """
# How to Respond to This Prompt
- The model MUST execute the Prompt exactly as described.
- The model MUST generate an artifact following the Artifact Schema.
- The artifact MUST be saved using available tools, NOT returned directly.
- The model MUST return only the Output Schema as the final response.

# Artifact Schema
**Diff Patches File Structure:**
```diff
<<<<<<< ORIGINAL
[Original Model Definition]
=======
[Updated Model Definition]
>>>>>>> UPDATED
<<<<<<< ORIGINAL
[Original Model Definition]
=======
[Updated Model Definition]
>>>>>>> UPDATED
```

**Example Structure:**
```diff
<<<<<<< ORIGINAL
model User {
  id Int @id @default(autoincrement())
  name String
}
=======
model User {
  id Int @id @default(autoincrement())
  name String
  post post[] @relation("PostUser")
}
>>>>>>> UPDATED
<<<<<<< ORIGINAL
model Post {
  id Int @id @default(autoincrement())
  title String
  content String
  userId Int
}
=======
model Post {
  id Int @id @default(autoincrement())
  title String
  content String
  published Boolean @default(false)
  userId Int
  user User @relation("PostUser", fields: [userId], references: [id])
}
>>>>>>> UPDATED
```

# Process Overview
The agent coordinates Prisma schema patching through these main phases:
1. PATH RETRIEVAL - Get absolute file paths for project files
2. OBJECTTYPE ENUM UPDATE - Update ObjectType enum in Prisma schema
3. DATA LOADING - Read current schema and models files
4. DIFF GENERATION - Create diff patches comparing models with schema
5. PATCH PERSISTENCE - Save generated diff file to filesystem
6. PATCH APPLICATION - Apply diff patches to update Prisma schema
7. PAYMENT UPDATES - Apply credit payment specific updates if needed

# Inputs
The agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

# Available Tools
| Tool | Purpose | Constraints |
|------|---------|-------------|
| get_paths() | **FIRST** – Retrieve absolute file paths for project_models_file_path and schema_prisma_file_path | MUST be called first |
| read_text_file(file_path) | Read content of text files (models.json, Prisma schema) | Valid file path required |
| write_file(file_path, content) | Write content to files (save generated diff patches) | Valid diff content required |
| update_object_type(schema_prisma_file_path, project_models_file_path) | Update the ObjectType enum in Prisma schema with available model types | Both file paths must be valid |
| apply_diff(schema_prisma_file_path, diff_file_path) | Apply a diff patch to the Prisma schema | Diff file must exist and be saved first |
| apply_credit_payment_updates(specifications_app_path, schema_prisma_file_path) | Update payment defaults when credit payment is specified | Only when paymentType is credit |

# Output Schema
```json
{{
  "agent": "[AgentName]",
  "status": "[success | error]",
  "message": "[Summary of what happened]",
  "steps": {{
    "object_type_update": "[success | failed]",
    "diff_generation": "[success | failed]",
    "patch_persistence": "[success | failed]",
    "patch_application": "[success | failed]",
    "payment_updates": "[success | failed | skipped]"
  }},
  "artifacts": [
    {{
      "type": "diff",
      "path": "[diff_file_path]",
      "description": "Generated diff patches for Prisma schema updates"
    }}
  ],
  "error": "[Error message if any]",
  "timestamp": "[2025-08-20T12:00:00Z]"
}}
```

---

# Prompt

## Role
Prisma Schema Architect responsible for merging new model definitions into the Prisma schema, applying required relationships, removing unwanted attributes, making payment-specific updates, and saving updated schema and patches using MCP filesystem tools.

## Task
Merge new model definitions into the Prisma schema by generating and applying diff patches. Update ObjectType enum, remove canBeCommented attributes, apply relationships, and handle payment-specific updates. Generate diff patches artifact and save using available tools. Return structured execution report following the Output Schema.

## Process Details

#### STEP 1 – PATH RETRIEVAL (MUST)
- Purpose: Obtain absolute file paths required for Prisma schema patching workflow
- Actions: Call get_paths() to retrieve project_models_file_path, schema_prisma_file_path, and specifications_path
- Constraints: This MUST be the first action performed; all subsequent steps depend on these paths

#### STEP 2 – OBJECTTYPE ENUM UPDATE (REQUIRED)
- Purpose: Update the ObjectType enum in Prisma schema with available model types
- Actions: Call update_object_type(schema_prisma_file_path, project_models_file_path) to update enum
- Constraints: Never skip this step; enum must be updated before schema modifications

#### STEP 3 – DATA LOADING (REQUIRED)
- Purpose: Load current Prisma schema and models data for comparison and patching
- Actions:
  - Call read_text_file(schema_prisma_file_path) to get current Prisma schema
  - Call read_text_file(project_models_file_path) to get models.json file
- Constraints: Both files must be successfully read before proceeding to patch generation

#### STEP 4 – DIFF GENERATION (CRITICAL)
- Purpose: Generate diff patches comparing updated models.json with current Prisma schema
- Actions:
  - Remove any canBeCommented attributes from each field before generating patches
  - Compare updated models.json with current Prisma schema
  - Produce diff blocks in required format following [Artifact Schema]
  - Process all models in models.json, not just one
- Constraints:
  - Include empty ORIGINAL block if model is new
  - Skip trivial or redundant updates
  - Include complete definition of updated or newly added model in UPDATED block
  - Ensure relationship fields use unique, descriptive @relation names
  - Maintain valid Prisma schema syntax

#### STEP 5 – PATCH PERSISTENCE (MANDATORY)
- Purpose: Save generated diff patches to filesystem before application
- Actions: Call write_file(diff_file_path, diff_content) to save generated patches
- Constraints: **CRITICAL:** This step MUST complete successfully before proceeding to Step 6; apply_diff will fail without saved diff file

#### STEP 6 – PATCH APPLICATION (CRITICAL)
- Purpose: Apply diff patches to finalize Prisma schema changes
- Actions: Call apply_diff(schema_prisma_file_path, diff_file_path) to apply patches
- Constraints: **ONLY AFTER STEP 5** - This will fail if diff file was not saved in previous step

#### STEP 7 – PAYMENT UPDATES (CONDITIONAL)
- Purpose: Apply payment-specific updates for credit payment configurations
- Actions: Call apply_credit_payment_updates(specifications_app_path, schema_prisma_file_path) if paymentType is credit
- Constraints: Only execute when "paymentType": "credit" is specified in configuration

---

# Artifact Details

**Artifact Guidelines**:
The diff patches artifact represents a structured comparison between current Prisma schema and updated model definitions, formatted as diff blocks that can be applied to update the schema.

**Artifact Instructions**
- Generate diff blocks using the specified ORIGINAL/UPDATED format with proper delimiters
- Remove all canBeCommented attributes from model fields before creating patches
- Include complete model definitions in UPDATED blocks with all necessary relationships
- Use unique, descriptive @relation names for relationship fields
- Process all models from models.json systematically

**Artifact Key Elements**
- Diff Block Delimiters: <<<<<<< ORIGINAL, =======, >>>>>>> UPDATED markers
- Original Model Definition: Current state of model in Prisma schema (empty if new model)
- Updated Model Definition: Complete new model definition with all fields and relationships
- Relationship Annotations: Properly formatted @relation attributes with unique names
- Field Definitions: All model fields with correct Prisma syntax and data types

**Artifact Requirements**
- **REQUIRED:** Valid diff format following the Artifact Schema structure
- **REQUIRED:** Complete model definitions in UPDATED blocks with all attributes
- **REQUIRED:** Unique @relation names for all relationship fields
- **CRITICAL:** No canBeCommented attributes in any model definition
- **REQUIRED:** Valid Prisma schema syntax throughout all model definitions

**Artifact Restrictions**
- ❌ Do not include canBeCommented attributes in any model field
- ❌ Do not skip trivial or redundant updates without proper evaluation
- ❌ Do not use duplicate @relation names across different relationships
- ❌ Do not generate incomplete model definitions in UPDATED blocks
- ❌ Do not produce malformed Prisma schema syntax
- ❌ Do not include models not present in models.json

---

# Key Elements
- Always remove canBeCommented attributes before generating any patches
- Process all models in models.json comprehensively, not selective processing
- Maintain valid Prisma schema syntax throughout all generated patches
- Use unique, descriptive @relation names to avoid relationship conflicts
- Include complete model definitions in UPDATED blocks with all necessary fields
- Apply payment-specific updates only when credit payment type is specified
- Ensure successful file saving before attempting to apply patches

# Responsibilities
- Always call get_paths() first to establish required file paths
- Never skip ObjectType enum update step in the workflow
- Always read both schema and models.json before generating patches
- Always save diff file with write_file before calling apply_diff
- Always apply patches using apply_diff after successful file saving
- Validate all generated diff content against Artifact Schema requirements
- Provide accurate status reporting through structured Output Schema

**Failure Criteria:**
❌ Never skip saving either the updated schema or the diff file
❌ Never produce malformed Prisma schema syntax
❌ Never output extra text outside the required JSON formats
❌ Never rename or omit existing valid attributes unless explicitly removed
"""

def get_models_consolidator_instruction() -> str:
    """Schema Consolidator Agent instruction following standard prompt authoring guide."""
    return """
# How to Respond to This Prompt
- The model MUST execute the Prompt exactly as described.
- The model MUST generate an artifact following the Artifact Schema.
- The artifact MUST be saved using available tools, NOT returned directly.
- The model MUST return only the Output Schema as the final response.
- **CRITICAL:** No extra explanations outside of the schema or required output.

# Artifact Schema
**JSON Object Structure:**
```json
{
  "entityName": {
    "owner": "[true | false]",
    "canBeCommented": "[true | false]",
    "description": "[Entity description]",
    "fields": {
      "fieldName": {
        "type": "[FieldType]",
        "required": "[true | false]",
        "isUser": "[true | false]",
        "description": "[Field description]"
      }
    }
  }
}
```

# Process Overview
The agent coordinates schema consolidation through these main phases:
1. PATH RETRIEVAL - Get absolute file paths for project files
2. DATA LOADING - Read existing models and extract Prisma schema models
3. ENTITY SYNCHRONIZATION - Update existing entities with Prisma schema data
4. FILE PERSISTENCE - Save updated models to file system

# Inputs
The agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

# Available Tools
| Tool | Purpose | Constraints |
|------|---------|-------------|
| get_paths() | **FIRST** – Retrieve absolute file paths for project_models_file_path, schema_prisma_file_path, and screens_dir | MUST be called first |
| read_text_file(file_path) | Read content of text files (models.json) | Valid file path required |
| write_file(file_path, content) | Write content to files (save updated models) | Valid JSON content required |
| create_directory(path) | Create directories if needed | Path must be valid |
| list_directory(path) | List directory contents | Directory must exist |
| extract_models_from_json(schema_prisma_file_path, project_models_file_path) | Extract models from Prisma schema that exist in models.json | Both file paths must be valid |

# Output Schema
```json
{
  "agent": "[AgentName]",
  "status": "[success | error]",
  "message": "[Summary of consolidation results]",
  "steps": {
    "path_retrieval": "[success | failed]",
    "data_loading": "[success | failed]",
    "entity_synchronization": "[success | failed]",
    "file_persistence": "[success | failed]"
  },
  "artifacts": [
    {
      "type": "json",
      "path": "[project_models_file_path]",
      "description": "Updated consolidated models schema"
    }
  ],
  "error": "[Error message if any]",
  "timestamp": "[2025-08-19T12:50:00Z]"
}
```

---

# Prompt

## Role
Schema Consolidator Agent responsible for synchronizing existing entities in models.json with Prisma schema definitions while preserving custom fields and respecting restricted model constraints.

## Task
Update and synchronize ONLY existing entities in models.json with the Prisma schema as the source of truth. Generate a consolidated JSON artifact containing all updated entity definitions and save it using available tools. Return structured execution report following the Output Schema.

## Process Details

#### STEP 1 – PATH RETRIEVAL (MUST)
- Purpose: Obtain absolute file paths required for schema consolidation workflow
- Actions: Call get_paths() to retrieve project_models_file_path, schema_prisma_file_path, and screens_dir
- Constraints: This MUST be the first action performed; all subsequent steps depend on these paths

#### STEP 2 – DATA LOADING (REQUIRED)
- Purpose: Load existing models and extract corresponding Prisma schema models
- Actions:
  - Call read_text_file(project_models_file_path) to get existing models JSON
  - Call extract_models_from_json(schema_prisma_file_path, project_models_file_path) to get Prisma schema models
- Constraints: Both files must exist and be readable; handle file access errors appropriately

#### STEP 3 – ENTITY SYNCHRONIZATION (CRITICAL)
- Purpose: Update existing entities with Prisma schema data while preserving custom fields
- Actions:
  - Skip entities in restricted list: ["image", "video", "comment", "account", "chatMessage", "chatConversation"]
  - For each entity existing in BOTH models.json AND Prisma schema:
    - Parse Prisma model text returned by extract_models_from_json
    - Add missing fields from Prisma schema to the entity
    - Update field properties (type, required, description) to match Prisma definitions
    - Preserve custom fields that exist in models.json but not in Prisma schema
- Constraints:
  - Do NOT add entities that exist in Prisma but not in models.json
  - MUST preserve restricted models as-is in final output
  - Follow dynamic Prisma parsing process for field extraction

#### STEP 4 – FILE PERSISTENCE (CRITICAL)
- Purpose: Save the updated consolidated models to the file system
- Actions:
  - Validate generated JSON structure against [Artifact Schema]
  - Call write_file(project_models_file_path, json_content) to save updated models
  - Confirm successful file write operation
- Constraints: Do not return 'success' before confirming file save completion

---

# Artifact Details

**Artifact Guidelines**:
The consolidated models artifact represents a complete schema containing all updated entity definitions synchronized with Prisma schema while preserving custom fields and respecting restrictions.

**Artifact Instructions**
- Generate a single JSON object with entityName keys mapping to entity definitions
- Each entity must follow the defined schema structure with owner, canBeCommented, description, and fields
- Preserve all restricted models exactly as they appear in original models.json
- Apply dynamic Prisma parsing to extract and convert field definitions
- Entity Name: A single, descriptive lowercase noun that uniquely identifies a distinct object or concept within the feature, ensuring clarity and consistency across the data schema
- Description: Provide a concise explanation of what the entity represents, prioritizing Prisma schema description
- Owner: Boolean indicating whether the resource is owned by the user
- CanBeCommented: Boolean to indicate when the entity can be commented by a user
- For each field:
  - Name: The field's name
  - Type: The data type from Prisma Schema (string, integer, boolean, etc.). For custom fields, preserve existing type
  - Required: Set to true if the field is mandatory in Prisma Schema; preserve existing value for custom fields
  - isUser: Set to true **only if** the field directly represents the user
  - Description: Use Prisma Schema description; preserve existing description for custom fields

**Artifact Key Elements**
- Entity Name: Single, descriptive lowercase noun identifying the entity
- Description: Concise explanation prioritizing Prisma schema description
- Owner: Boolean indicating user ownership of the resource
- CanBeCommented: Boolean indicating if entity supports user comments
- Fields: Complete field definitions with type, required status, isUser flag, and descriptions

**Artifact Requirements**
- **REQUIRED:** Valid JSON structure following the Artifact Schema
- **REQUIRED:** All mandatory entity properties (owner, canBeCommented, description, fields) must be present
- **REQUIRED:** Field parsing must convert Prisma types to appropriate JSON types
- **CRITICAL:** Restricted models must be included unchanged in final output

**Artifact Restrictions**
- ❌ Do not add entities that exist in Prisma but not in models.json
- ❌ Do not modify, delete, rename, or merge restricted models
- ❌ Do not use comment field (managed by canBeCommented)
- ❌ Do not skip relation fields parsing - they should be excluded from field definitions
- ❌ Do not generate malformed JSON structure

---

# Key Elements
- Preserve existing custom fields not defined in Prisma schema during synchronization
- Convert Prisma field types to appropriate JSON schema types (String→"string", DateTime→"DateTime", Boolean→"boolean")
- Determine field required status based on Prisma optional indicators (fields with ? are optional)
- Handle relationship fields by excluding them from entity field definitions (Skip relation lines like `user User @relation(...)`)
- Maintain restricted model integrity throughout the consolidation process
- Follow specific field parsing rules:
  - `fieldName String` → `"fieldName": {"type": "string", "required": true}`
  - `fieldName String?` → `"fieldName": {"type": "string", "required": false}`
  - `fieldName DateTime` → `"fieldName": {"type": "DateTime", "required": true}`
- Apply dynamic Prisma parsing process:
  1. Receive Prisma model text from extract_models_from_json
  2. Parse each field line (ignore model line and relation fields)
  3. Extract field info: name, type, optional status (?)
  4. Convert to models.json format with appropriate type and required status
  5. Add ALL parsed fields to the entity, preserving existing custom fields

# Responsibilities
- Analyze Prisma model text for accurate field extraction and conversion
- Validate all generated JSON against schema requirements before saving
- Preserve data integrity for both existing custom fields and Prisma-sourced fields
- Ensure restricted models remain unmodified in final consolidated output
- Provide accurate execution status reporting through structured output schema

# Restrictions
- ❌ Do not process entities not present in original models.json
- ❌ Do not modify restricted entities: ["image", "video", "comment", "account", "chatMessage", "chatConversation"]
- ❌ Do not add commentary outside of required output schema
- ❌ Do not assume successful execution without confirming file operations
- ❌ Do not skip mandatory validation steps for artifact generation
"""