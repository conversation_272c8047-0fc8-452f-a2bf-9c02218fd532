"""
Features Manager Agent - Feature Specification Generation Coordinator
=================================================================
"""

from google.adk.agents import LlmAgent
from .prompt import get_routing_instruction, get_feature_generator_instruction
from .tools import get_feature_tools

routing_manager = LlmAgent(
    name="routing_manager",
    model="gemini-2.5-flash",
    description="Expert in creating and managing routing specifications for React applications. Handles route creation, updates, and conflict resolution.",
    instruction=get_routing_instruction(),
    tools=get_feature_tools()
)

feature_generator = LlmAgent(
    name="feature_generator",
    model="gemini-2.5-flash",
    description="Expert in generating comprehensive feature specifications based on user stories, payment restrictions, and other project requirements.",
    instruction=get_feature_generator_instruction(),
    tools=get_feature_tools()
)
