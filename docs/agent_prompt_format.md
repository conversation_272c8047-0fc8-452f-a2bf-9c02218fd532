# 📘 Prompt Authoring Guide

A standard for writing high-quality prompts across this codebase.
This guide enforces a consistent structure with explicit sections such as Prompt, Input, Tools, Artifact Schema, Artifact, and Output Schema.
It ensures prompts are reusable, consistent, and compatible in multi-agent workflows.

---

## Why This Guide
- Consistent authoring across prompts and teams.
- Clear expectations for AI output and structure.
- Reusable, composable tasks with explicit constraints.
- Prevents ambiguity and enforces measurable outcomes.

---

## Core Principles
- Output MUST strictly follow the declared schema.
- Steps MUST be written in ALL CAPS TITLES with strong keywords (MUST, REQUIRED, CRITICAL).
- Keep outputs concise, skimmable, and actionable.
- Favor specificity over generalities; avoid buzzwords and platitudes.
- Use square brackets [Element Name] to reference schema elements, sections, or concepts defined elsewhere.
- Use single quotes 'literal value' only for literal values, examples, or direct text that should appear as-is.

---

## Standard Prompt Structure (Explanation)

### 1) How to Respond to This Prompt
- Defines the global contract for the agent.
- These rules NEVER change across prompts.
- Example rules:
  - The model MUST execute the Prompt exactly as described.
  - The model MUST generate an artifact following the Artifact Schema.
  - The artifact MUST be saved using available tools, NOT returned directly.
  - The model MUST return only the Output Schema as the final response.
  - CRITICAL: No extra explanations outside of the schema or required output.

---

### 2) Prompt

This is the heart of the runtime mission. It defines the role, objectives, and process of the agent.

#### Role
- Defines the identity and scope of the model.
- Must be precise, e.g. "UI Extractor Agent" or "Reviewer Agent".

#### Task
- High-level description of what the agent must achieve.
- Must explicitly state what artifact(s) are produced and what structured response must be returned.

#### Process Overview
- Short roadmap of the mission in 3–5 steps.
- Each step = one sentence max.
- Example style (do not use real data):
  1. [STEP NAME] – [one-line summary].
  2. [STEP NAME] – [one-line summary].
  3. [STEP NAME] – [one-line summary].

#### Artifact Schema
- Defines the structure of the main deliverable (artifact).
- Separate from the execution report (Output Schema).
- Must specify constraints (required fields, formatting, validation).

**Example (JSON):**
```json
{
  "purpose": "[Purpose]",
  "features": ["[Feature List]"]
}
```

**Example (Markdown):**
```markdown
# [Report Title]
## [Section Name]
- [Item Description]
```

#### Process Details
- Expands the Process Overview into detailed instructions.
- Each step MUST use ALL CAPS TITLES and contain:
  - Purpose (why this step exists)
  - Actions (what the model must do)
  - Constraints (rules or limitations for this step)

Example style:

##### STEP 1 – [STEP NAME]
- Purpose: [describe purpose]
- Actions: [list concrete actions]
- Constraints: [list restrictions]

#### Artifact Instructions
- Key directives for generating the artifact.
- Example: "Use the [Artifact Schema] to structure the final output."

#### Artifact Guidelines
- Global rules for artifact generation.
- Example: "Artifact must be valid JSON before saving."

#### Artifact Restrictions
- Explicit prohibitions.
- Example:
  - ❌ Do not skip required fields.
  - ❌ Do not generate malformed JSON.
  - ❌ Do not output outside of schema.

---

### 3) Inputs
- Defines what the model requires as input during execution.
- Can be JSON, plain text, or structured fields.
- Must clearly specify required and optional fields.

---

### 4) Output Schema
- Defines the execution report structure.
- Separate from the main artifact.
- Must include status, error handling, and metadata (like timestamp).

**Example:**
```json
{
  "agent": "[AgentName]",
  "status": "[success | error]",
  "message": "[Summary of what happened]",
  "steps": {
    "step_name": "[success | failed]"
  },
  "artifacts": [
    {
      "type": "[file | json | text | other]",
      "path": "[optional]",
      "description": "[what was produced]"
    }
  ],
  "error": "[Error message if any]",
  "timestamp": "[2025-08-19T12:50:00Z]"
}
```

---

### 5) Available Tools
- Lists all tools the model can access.
- Each tool must be described with purpose + constraints.
- Example:
  - save_json_file: Saves a JSON artifact to disk. Must only accept valid JSON.

---

## 📋 Template (Copy-Paste)

```markdown
# How to Respond to This Prompt
- The model MUST execute the Prompt exactly as described.
- The model MUST generate an artifact following the Artifact Schema.
- The artifact MUST be saved using available tools, NOT returned directly.
- The model MUST return only the Output Schema as the final response.
- CRITICAL: No extra explanations outside of the schema or required output.

## Prompt

### Role
{ define the model's role here }

### Task
{ describe objectives and outputs here }

### Process Overview
1. [STEP NAME] – [one-line summary]
2. [STEP NAME] – [one-line summary]
3. [STEP NAME] – [one-line summary]

### Artifact Schema
{ define the structure of the main deliverable here }

**Example (JSON):**
```json
{
  "example_field": "[value]"
}
```

### Process Details

#### STEP 1 – [STEP NAME]
- Purpose: [describe purpose]
- Actions: [list actions]
- Constraints: [list restrictions]

#### STEP 2 – [STEP NAME]
- Purpose: …
- Actions: …
- Constraints: …

### Artifact Instructions
{ how to generate the artifact }

### Artifact Guidelines
{ global formatting and validation rules }

### Artifact Restrictions
- ❌ Do not skip required fields
- ❌ Do not generate malformed JSON
- ❌ Do not assume success without save confirmation

---

## Inputs
{ define required inputs here }

---

## Output Schema
{ define execution report schema here }

**Example:**
```json
{
  "agent": "[AgentName]",
  "status": "[success | error]"
}
```

---

## Available Tools
{ list tools and constraints here }
```